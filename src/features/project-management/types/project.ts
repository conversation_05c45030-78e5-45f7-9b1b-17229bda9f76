import type { IFileResponse } from '@/shared/types/global';

export enum ProjectCampaignEnum {
  CORPORATE,
  CRISIS_MANAGEMENT,
  EVENT,
  GR_ADVOCACY,
  IMC,
  MARKET_RESEARCH,
  MEDIA_RELATION_PR,
  MI_BRAND_BRANDING,
  PRODUCT_LAUNCH,
  SOCIAL_DIGITAL_CORPORATE,
  SOCIAL_DIGITAL_PRODUCT,
  TVC_VIDEO_PRODUCTION,
}

export enum ProjectStatusEnum {
  PLANNED,
  IN_PROGRESS,
  COMPLETED,
  ON_HOLD,
}

export enum ProjectTypeEnum {
  BRANDING,
  GENERAL_CONSULTING,
  DIAGNOSTICS,
}

// Type definitions
type DateRange = {
  from: Date | undefined;
  to: Date | undefined;
};

export type FilterState = {
  status: ProjectStatusEnum | 'All';
  type: ProjectTypeEnum | 'All';
  campaign: ProjectCampaignEnum | 'All';
  startDateRange: DateRange;
  endDateRange: DateRange;
  searchQuery: string;
};

export type SortOption = 'latest' | 'oldest';

export type Project = {
  id: string;
  name: string;
  slugName: string;
  description?: string;
  status: ProjectStatusEnum;
  type: ProjectTypeEnum;
  campaign: ProjectCampaignEnum;
  startDate: string;
  endDate?: string;

  // Client information
  clientName: string;
  address: string;
  taxCode: string;
  contactPerson: string;
  tel: string;
  email: string;
  industry?: string;

  // Team information
  ownedId?: string;
  memberIds: string[];

  // Metadata
  createdAt: string;
  updatedAt: string;
};

export type GetProjectsResponse = {
  items: Project[];
  total: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type questionResponseCrew = {
  question: string;
  answer: string;
  id: string;
};

export type documentUrlCrew = {
  id: string;
  url: string;
  originalname: string;
  filename: string;
  key: string;
};

export type fileUploadResponse = {
  infos: stepInfoFile[];
  model: string;
};

export type stepInfoFile = {
  files: {
    file: string;
    name: string;
    type: string;
    id: string;
  }[];
  serviceOption: ProjectCampaignEnum;
};

export type stepInfosMarkdownResponse = {
  infos: { value: string }[];
  isGenerate: boolean;
  model: string;
  stepId: string;
};

export type TemplateFiles = {
  type: ETypeFile.BRIEF_QUESTION | ETypeFile.BRIEF_TEMPLATE | ETypeFile.QUOTATION;
  file: IFileResponse;
};

export enum ETypeFile {
  BRIEF_QUESTION = 'brief_question',
  BRIEF_TEMPLATE = 'brief_template',
  QUOTATION = 'quotation',
}

export type documentFileUpload = {
  infos: stepInfoDocumentFile[];
  order: number;
};

export type discoveryQuestionnaire = {
  infos: any;
  order: number;
};

export type stepInfoDocumentFile = {
  files: {
    file: string;
    name: string;
    type: string;
    id: string;
  }[];
  typeSelected: string[];
};

export type ScoringReportDataType = {
  report: string;
  score: number;
};
