'use client';

import type { EvaluationCriteria, OverallScore } from '../../../types/evaluation-form';

import { useCoAgent, useCopilotChat } from '@copilotkit/react-core';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import {
  useClientProfile,
  useCurrentStep,
  useCurrentStepInfoIds,
  useCurrentTask,
  useEvaluationActions,
  useOverallScore,
  useScoreDetail,
  useWorkflowActions,
} from '../../../stores/project-workflow-store';
import { SectionType } from '../../../types/evaluation-form';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import EvaluationTable from './EvaluationTable';
import { Role, TextMessage } from '@copilotkit/runtime-client-gql';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import type { QAStrategistOutput, ScoreDetail } from '@/features/project-management/types/evaluation';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useGetInfoDetail, useProjectDetail } from '@/features/project-management/hooks';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import type { infosQA } from '@/features/project-management/types/step';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME, MESSAGE_SEND_ROUTE_AGENT } from '@/shared/constants/global';
import { EEndpointApiCopilotkit, ENameStateAgentCopilotkit } from '@/shared/enums/global';
import type { IFileResponse, stateRouteAgent } from '@/shared/types/global';
import { processEvaluationData } from '@/features/project-management/utils/workflowUtils';
import { collaborationSection, financialCapacitySection, growthPotentialSection, initialEvaluationData } from '@/features/project-management/constants/mock-section';
import { Button } from '@/shared/components/ui/button';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { useQueryClient } from '@tanstack/react-query';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import MessageWarning from '../common/MessageWarning';
import SelectModelAI from '../common/SelectModelAI';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';

// Zod schema for form validation - defined outside component to prevent recreation
const evaluationFormSchema = z.object({
  evaluationData: z.array(
    z.object({
      id: z.string(),
      criteria: z.string(),
      answer: z.string(),
      confidence: z.string().optional(),
      citation: z.string().optional(),
      criteriaType: z.string(),
      weight: z.string(),
      criteriaScore: z.string(),
      convertedScore: z.string(),
      type: z.string(),
      selectedOptionIndex: z.number().optional(),
    }),
  ),
});

type EvaluationFormData = z.infer<typeof evaluationFormSchema>;

// Memoized section component to prevent unnecessary re-renders
const EvaluationSection = React.memo(({
  title,
  sectionType,
  errors,
  disabled,
  onChangeData,
}: {
  title: string;
  sectionType: SectionType;
  errors: any;
  disabled: boolean;
  onChangeData: () => void;
}) => {
  return (
    <>
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <div className="space-y-6">
        <EvaluationTable sectionType={sectionType} disabled={disabled} onChangeData={onChangeData} />

        {errors?.evaluationData && (
          <p className="text-red-500 text-sm mt-2">
            {errors.evaluationData.message}
          </p>
        )}
      </div>
    </>
  );
});

EvaluationSection.displayName = 'EvaluationSection';

const EvaluationForm: React.FC = () => {
  // USING CUSTOM HOOK
  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const currentStepInfoIds = useCurrentStepInfoIds();

  const clientProfile = useClientProfile();

  const overallScore = useOverallScore();

  const scoreDetailStore = useScoreDetail();

  // const hide = useChatBoxHide();

  const { mutateAsync } = useUpdateStatusStep();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const {
    // updateStatus,
    completeStep,
    updateStepData,
    initializeEvaluation,
    getFinalOverallScore,
    updateInitialEvaluationData,
    updateScoreDetail,
    // getNextStepId,
  } = useEvaluationActions();

  // USING useState

  const [isTaskRunning, setIsTaskRunning] = useState(true);

  const [isViewMode, setIsViewMode] = useState(false);

  const [isEditing, setIsEditing] = useState(false);

  const [isShowModal, setIsShowModal] = useState(false);

  const [isSaved, _setIsSaved] = useState(true);

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const titleConfirm = 'Confirm Changes';

  const titleUnSave = 'UnSave Changes';

  const descriptionUnSave = 'The changes made will be lost. Do you want to proceed?';

  const descriptionConfirm = 'Are you sure you want to make this change? Changing the status will result in the deletion of all related information for this step';

  const [titlePopup, setTitlePopUp] = useState<string>(titleConfirm);

  const [descriptionPopUp, setDescriptionPopUp] = useState<string>(descriptionConfirm);

  const [isClickUnSaved, setIsClickUnSaved] = useState(false);

  // USING COPILOTKIT

  const { state: _coAgentState, setState: setCoAgentsState, running } = useCoAgent<stateRouteAgent<any>>({
    name: AGENT_ROUTE_NAME,
    initialState: {},
  });

  const { appendMessage } = useCopilotChat();

  // GET ID PARAMS

  const params = useParams<{ id: string }>();

  const { data: scoreDetail } = useGetInfoDetail<ScoreDetail, infosQA>(currentStep?.id ?? '');

  const { data: project } = useProjectDetail(params.id);

  const { registerStep, clearStep } = useDirty();

  const {
    updateStatus,
    getNextStepId,
  } = useWorkflowActions();

  const queryClient = useQueryClient();

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes in the form. Are you sure you want to leave?',
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  // Get the client profile section as the evaluationCriteria for backward compatibility
  const evaluationCriteria = useMemo(() => clientProfile, [clientProfile]);

  // Initialize the store when the component mounts - only once
  useEffect(() => {
    initializeEvaluation();
  }, [initializeEvaluation]);

  // Initialize React Hook Form with Zod validation
  const {
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<EvaluationFormData>({
    resolver: zodResolver(evaluationFormSchema),
    defaultValues: {
      evaluationData: evaluationCriteria,
    },
  });

  // Update form values when evaluation criteria change - with debounce to prevent excessive updates
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setValue('evaluationData', evaluationCriteria);
    }, 100);
    return () => clearTimeout(timeoutId);
  }, [evaluationCriteria, setValue]);

  useEffect(() => {
    if (scoreDetail) {
      updateScoreDetail(scoreDetail);
    }
  }, [scoreDetail, updateScoreDetail]);

  const handleSendMessage = useCallback(() => {
    if (scoreDetail?.stepInfoPrevious) {
      const previousStep = scoreDetail.stepInfoPrevious[0]?.infos ?? [];
      const documentUrl = previousStep[previousStep.length - 1];
      const questions = previousStep.slice(0, -1);

      const document_url = (documentUrl?.url ?? []).map((url: any) => ({
        id: url.id,
        url: url.url,
        originalname: url.name,
        filename: url.name,
        key: url.file,
      }));

      setCoAgentsState((prevState: any) => ({
        ...prevState,
        agent_name: AGENT_NAME_COPILOTKIT.SUMMARIZE,
        [ENameStateAgentCopilotkit.SUMMARIZE]: {
          ...prevState[ENameStateAgentCopilotkit.SUMMARIZE],
          initial_info: [...questions],
          document_url: [...document_url],
        },
      }));

      appendMessage(
        new TextMessage({
          content: MESSAGE_SEND_ROUTE_AGENT,
          role: Role.Developer,
        }),
      );
    }
  }, [scoreDetail, appendMessage]);

  // Re-Running copilotkit when data in DB
  useEffect(() => {
    if (!running && currentStep?.status !== EStatusTask.COMPLETED) {
      // FIXME: update later when using Copilotkit
      // handleSendMessage();
    }
  }, [running, currentStep, handleSendMessage]);

  const changeEditMode = (status: boolean) => {
    setIsEditing(status);
  };

  const changeStatusTaskRun = (status: boolean) => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setIsTaskRunning(status);
  };

  const getPayloadCreateScore = (overallScore: OverallScore) => {
    const keys = [
      'clientProfileSection',
      'financialCapacitySection',
      'collaborationSection',
      'growthPotentialSection',
    ];
    const { data } = overallScore;
    return keys.map((key, index) => {
      const score: EvaluationCriteria[] = data[key as keyof typeof data];
      return {
        order: index,
        name: `name_${index}`,
        scores: score.map(item => ({
          criteria: item.criteria,
          type: item.type,
          answer: item.answer,
          confidence: item.confidence || '',
          citation: item.citation || '',
          criteriaType: item.criteriaType,
          weight: item.weight,
          criteriaScore: item.criteriaScore,
        })),
      };
    });
  };

  const saveDataFromAI = React.useCallback(async (data: QAStrategistOutput) => {
    const initData = {
      initialEvaluationData: [...initialEvaluationData],
      financialCapacitySection: [...financialCapacitySection],
      collaborationSection: [...collaborationSection],
      growthPotentialSection: [...growthPotentialSection],
    };
    const { overallScore } = processEvaluationData(initData, data);
    const stepInfos = getPayloadCreateScore(overallScore).map(item => ({
      order: item.order,
      infos: item.scores,
      model: modelAISelected,
    }));

    await updateQuestionAnswer({
      stepInfos,
    }, currentStep?.id ?? '');

    mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.IN_PROGRESS });
  }, []);

  const getSummarizeData = React.useCallback(async () => {
    if (scoreDetail && scoreDetail.stepInfoPrevious.length && project) {
      const qaInfos = scoreDetail.stepInfoPrevious[0]?.infos ?? [];

      const questions = qaInfos?.slice(0, -1);
      const projectId = project?.id ?? '';
      const filesResponse = (qaInfos[qaInfos.length - 1]?.url ?? []) as IFileResponse[];

      const data = {
        project_id: projectId,
        llm: modelAISelected,
        project_info: { ...project },
        document_url: [...(getFile(filesResponse, true))],
        initial_info: [...questions],
      };
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      try {
        const baseUrl = window.location.origin;
        const abortController = new AbortController();
        abortControllerRef.current = abortController;

        const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
          method: 'POST',
          body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.SUMMARIZE }),
          signal: abortControllerRef.current.signal,
        });

        const res = await response.json();
        const qaOutput = res.data.result;
        updateInitialEvaluationData(qaOutput);
        saveDataFromAI(qaOutput);
        changeEditMode(true);
        changeStatusTaskRun(false);
        clearStep(currentStep?.id ?? '');
      } catch (error: any) {
        // toast.error(error.message, {
        //   duration: 3000,
        // });
        console.log(error);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [project, scoreDetail]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (scoreDetailStore && Object.keys(scoreDetailStore).length) {
      updateInitialEvaluationData(scoreDetailStore as any);
      changeStatusTaskRun(false);

      if (currentStep?.status !== EStatusTask.COMPLETED) {
        changeEditMode(true);
      } else {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsViewMode(true);
        changeEditMode(false);
      }
    } else {
      changeStatusTaskRun(true);
    }
  }, [scoreDetailStore, currentStep, updateInitialEvaluationData]);

  useEffect(() => {
    if (!scoreDetail?.stepInfo.length && scoreDetailStore) {
      getSummarizeData();
    }
    if (scoreDetail?.stepInfoPrevious.length) {
      const infos = scoreDetail.stepInfoPrevious[0]?.infos ?? [];
      const modelSelected = (infos[infos.length - 1]?.modelAI ?? EValueModelAI.GPT);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAIDefault(modelSelected);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAISelected(modelSelected);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scoreDetailStore]);

  // FIXME: update later when using Copilotkit
  // useEffect(() => {
  //   const summarizeState = coAgentState[ENameStateAgentCopilotkit.SUMMARIZE];
  //   console.log(summarizeState);
  //   if (summarizeState && summarizeState.client_summarize_process && summarizeState.client_summarize_process === 'done' && isTaskRunning) {
  //     updateInitialEvaluationData(summarizeState.qa_strategist_output);
  //     changeEditMode(true);
  //     changeStatusTaskRun(false);
  //   }
  // }, [coAgentState, isTaskRunning, updateInitialEvaluationData, updateStatus, currentStep?.id]);

  // const setValueFiveTStateAgent = (overallScore: OverallScore) => {
  //   const { data } = overallScore;
  //   const items = Object.values(data).flat();
  //   return items.reduce((acc, i) => {
  //     const positionKey = GET_POSITION_BY_TYPE[i.type as keyof typeof GET_POSITION_BY_TYPE];
  //     acc[positionKey] = { ...i, id: positionKey.toString() };
  //     return acc;
  //   }, {} as Record<string, typeof items[number]>);
  // };

  // const sendStateToAgent = (five_t_input: Record<string, EvaluationCriteria>) => {
  //   setCoAgentsState(
  //     preAgent => (
  //       {
  //         ...preAgent,
  //         agent_name: AGENT_NAME_COPILOTKIT.ASSESSMENT,
  //         [ENameStateAgentCopilotkit.ASSESSMENT]: {
  //           five_t_input,
  //         },
  //       }
  //     ),
  //   );

  //   appendMessage(
  //     new TextMessage({
  //       content: MESSAGE_SEND_ROUTE_AGENT,
  //       role: Role.Developer,
  //     }),
  //   );
  // };

  // FIXME: update later when using Copilotkit
  const resetEvaluationData = () => {
    updateInitialEvaluationData(scoreDetailStore as any);
  };

  const toggleViewMode = () => {
    const initialStepInfos = (scoreDetail?.stepInfo ?? []).map(item => ({
      order: item.order,
      infos: item.infos,
    }));
    const latestScore = overallScore;

    const stepInfos = getPayloadCreateScore(latestScore).map(item => ({
      order: item.order,
      infos: item.scores,
    }));

    const isChanged = compareObjectArray(stepInfos, initialStepInfos);

    if (isEditing && !isChanged) {
      setTitlePopUp(titleUnSave);
      setDescriptionPopUp(descriptionUnSave);
      setIsClickUnSaved(true);
      setIsShowModal(true);
      return;
      // resetEvaluationData();
    }

    if (isClickUnSaved) {
      setIsClickUnSaved(false);
    }
    setIsEditing(prev => !prev);
  };

  const handleFinishStep = async () => {
    if (!currentStep) {
      return;
    }
    const latestScore = overallScore;

    const stepInfos = getPayloadCreateScore(latestScore).map(item => ({
      order: item.order,
      infos: item.scores,
      model: modelAISelected,
    }));

    await updateQuestionAnswer({
      stepInfos,
    }, currentStep.id);

    toast.success('Score calculation updated', {
      duration: 3000,
    });

    completeStep(currentStep.id);
  };

  const handleConfirmPopUp = async () => {
    if (isClickUnSaved) {
      resetEvaluationData();
      setIsEditing(false);
      setIsShowModal(false);

      return;
    }

    const infos = scoreDetail!.stepInfoPrevious[0]?.infos ?? [];

    const payload = infos.map((t, idx) => {
      if (idx === infos.length - 1) {
        return {
          ...t,
          modelAI: modelAISelected,
        };
      }
      return t;
    });

    clearStep(currentStep?.id ?? '');
    const ids = currentTask?.children[2]?.id ?? '';

    if (currentStep!.status !== EStatusTask.COMPLETED) {
      await mutateAsync({
        id: currentTask?.children[0]?.id ?? '',
        status: EStatusTask.COMPLETED,
        stepIds: [currentStep?.id ?? ''],
        select: 'all',
        stepInfoIds: [],
      });

      await updateQuestionAnswer({
        stepInfos: [
          {
            order: 0,
            infos: payload,
            model: modelAISelected,
          },
        ],
      }, currentTask?.children[0]?.id ?? '');

      await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', currentStep!.id], type: 'all' });
      setIsShowModal(false);

      return;
    }
    updateStatus(ids, EStatusTask.PENDING);
    const select = 'all';

    await mutateAsync({
      id: currentStep?.id ?? '',
      status: EStatusTask.PENDING,
      stepIds: [ids],
      select,
      stepInfoIds: [],
    });

    await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', getNextStepId()], type: 'all' });
    await handleFinishStep();
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const onChangeData = () => {
    // getFinalOverallScore();
  };

  useEffect(() => {
    if (!currentStep) {
      return;
    }

    if (overallScore.score === 0) {
      return;
    }
    const initialStepInfos = (scoreDetail?.stepInfo ?? []).map(item => ({
      order: item.order,
      infos: item.infos,
    })).sort((a, b) => a.order - b.order);
    const latestScore = overallScore;

    const stepInfos = getPayloadCreateScore(latestScore).map(item => ({
      order: item.order,
      infos: item.scores,
    }));
    const isChanged = compareObjectArray(stepInfos, initialStepInfos);

    const isChangedModel = modelAIDefault !== modelAISelected;
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    _setIsSaved(!((!isChanged || isChangedModel) && isEditing));

    registerStep(currentStep.id ?? '', () => ((!isChanged || isChangedModel) && isEditing));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [overallScore, isEditing, scoreDetail, modelAISelected]);

  // Form submission handler - wrapped in useCallback to prevent recreation on each render
  const onSubmit = useCallback(async () => {
    if (!currentStep) {
      return;
    }

    // Check if all criteria have been evaluated
    const allEvaluated = evaluationCriteria.every((item: { answer: string }) => item.answer !== '');

    if (!allEvaluated) {
      toast.error('Please evaluate all criteria before submitting', {
        duration: 3000,
      });
      return;
    }

    // Calculate the final score first
    getFinalOverallScore();

    // Get the latest overall score after calculation
    const latestScore = overallScore;
    // const five_t_input = setValueFiveTStateAgent(overallScore);

    // Update step data with the latest score

    // if (currentStep.status === EStatusTask.COMPLETED) {
    //   updateStepData(currentStep.id, latestScore);
    //   completeStep(currentStep.id);
    //   return;
    // }

    const stepInfos = getPayloadCreateScore(latestScore).map(item => ({
      order: item.order,
      infos: item.scores,
    }));

    const initialStepInfos = (scoreDetail?.stepInfo ?? []).map(item => ({
      order: item.order,
      infos: item.infos,
    }));

    const isChanged = compareObjectArray(stepInfos, initialStepInfos);

    const isChangedModel = modelAIDefault !== modelAISelected;

    if ((isEditing && !isChanged && currentStep.status === EStatusTask.COMPLETED)
      || isChangedModel) {
      setTitlePopUp(titleConfirm);
      setDescriptionPopUp(descriptionConfirm);
      setIsShowModal(true);
      return;
    }

    if (currentStep.status !== EStatusTask.COMPLETED) {
      handleFinishStep();
      await mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }
    updateStepData(currentStep.id, latestScore);

    // sendStateToAgent(five_t_input);
    // hide();
    clearStep(currentStep.id ?? '');
    if (currentStep.status === EStatusTask.COMPLETED) {
      completeStep(currentStep.id);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentStep,
    currentStepInfoIds,
    modelAISelected,
    evaluationCriteria,
    getFinalOverallScore,
    overallScore,
    updateStepData,
    completeStep,
    params.id,
  ]);

  // Memoize the form submission handler
  const onCompleteHandler = useMemo(() => handleSubmit(onSubmit), [handleSubmit, onSubmit]);

  const handleChangeModelAI = (value: string) => {
    setModelAISelected(value);
  };

  return (
    (isTaskRunning)
      ? (
          <div className="p-4 md:p-6 ">
            <div className="mb-1 md:mb-2">Analyzing</div>
            <ProjectCardSkeleton />

            <MessageWarning />

            {/* <WorkflowNavigation
              onComplete={onCompleteHandler}
              disableNext={isSubmitting || isTaskRunning}
              nextButtonText={isSubmitting ? 'Processing...' : 'Approve'}
              showPrevious={false}
            /> */}
          </div>
        )
      : (
          <>
            <form
              onSubmit={onCompleteHandler}
              className="relative space-y-6 border border-border rounded-lg p-4 md:p-6 m-4 md:m-6"
            >
              <SelectModelAI
                onChangeModel={handleChangeModelAI}
                defaultValue={modelAIDefault}
                disable={!isEditing}
              />

              {/* Section 1 */}
              <EvaluationSection
                title="Section A - Client Profile"
                sectionType={SectionType.CLIENT_PROFILE}
                errors={errors}
                disabled={!isEditing}
                onChangeData={onChangeData}
              />

              <div className="mt-8 border-t border-border pt-6"></div>

              {/* Section 2 */}
              <EvaluationSection
                title="Section B - Financial Capacity & Collaboration History"
                sectionType={SectionType.FINANCIAL_CAPACITY}
                errors={errors}
                disabled={!isEditing}
                onChangeData={onChangeData}
              />

              <div className="mt-8 border-t border-border pt-6"></div>

              {/* Section 3 */}
              <EvaluationSection
                title="Section C - Collaboration & Working Process"
                sectionType={SectionType.COLLABORATION}
                errors={errors}
                disabled={!isEditing}
                onChangeData={onChangeData}
              />

              <div className="mt-8 border-t border-border pt-6"></div>

              {/* Section 4 */}
              <EvaluationSection
                title="Section D - Growth Potential & Strategic Value"
                sectionType={SectionType.GROWTH_POTENTIAL}
                errors={errors}
                disabled={!isEditing}
                onChangeData={onChangeData}
              />
            </form>

            <WorkflowNavigation
              onComplete={onCompleteHandler}
              disableNext={isSubmitting}
              nextButtonText={isSubmitting ? 'Processing...' : (!isEditing && isViewMode) ? 'Next Step' : 'Approve'}
              showPrevious={true}
            >
              { isViewMode && (
                <Button
                  type="button"
                  className={`bg-warning-400 text-white rounded-lg hover:bg-warning-500 ${isEditing ? 'bg-error-500 hover:bg-error-600' : ''}`}
                  onClick={toggleViewMode}
                  disabled={isSubmitting}
                >
                  {isEditing ? 'Cancel' : 'Edit'}
                </Button>
              )}
            </WorkflowNavigation>

            {/* Modal for confirm content */}
            <GuardConfirmationModal
              open={isShowModal}
              onOpenChange={() => {}}
              title={titlePopup}
              description={descriptionPopUp}
              onConfirm={() => handleConfirmPopUp()}
              onCancel={() => handleCancelPopUp()}
              confirmText="Continue"
              cancelText="Cancel"
            />

            {/* Modal for guard */}
            <GuardConfirmationModal
              open={showDialog}
              onOpenChange={() => {}}
              title={title}
              description={message}
              onConfirm={onConfirm}
              onCancel={onCancel}
            />
          </>
        )

  );
};

export default EvaluationForm;
