import { Button } from '@/shared/components/ui/button';
import FileUpload from '../../initial-screening-form/FileUpload';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import QuestionnaireAnalysis from '../../discovery-questionnaire/QuestionnaireAnalysis';
import { EQuantitative } from '@/features/project-management/types/questionnaire';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import type { IFileResponse } from '@/shared/types/global';
import { useParams } from 'next/navigation';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { useProjectStatisticRaw } from '@/features/project-management/hooks/useProjectStatisticRaw';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import type { ScoringReportDataType } from '@/features/project-management/types/project';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import MessageWarning from '../../common/MessageWarning';

export type AnalysisReportQuantitativeRef = {
  getAnalysis: () => void;
};

type AnalysisReportQuantitativeType = {
  dataAnalysis: string;
  id: string;
  stepId: string;
  fileAnalysis: IFileResponse[];
  idQuestionnaire: string;
  templates: IFileResponse[];
  isAnalysis: boolean;
  isFinish: boolean;
  evaluationFramework: string;
  idScoring?: string;
  idAnalysis?: string;
  modelSelected: string;
  modelDefault: string;
  isLoading: boolean;
  ref?: React.Ref<AnalysisReportQuantitativeRef>;
  setIsLoading: (status: boolean) => void;
  setIsAnalysis: (status: boolean) => void;
  setScoringData: (data: ScoringReportDataType | null) => void;
};

const AnalysisReportQuantitative: React.FC<AnalysisReportQuantitativeType> = ({
  dataAnalysis,
  id,
  stepId,
  fileAnalysis,
  idQuestionnaire,
  templates,
  isAnalysis,
  isFinish,
  evaluationFramework,
  idScoring,
  idAnalysis,
  modelDefault,
  modelSelected,
  isLoading,
  ref,
  setIsLoading,
  setIsAnalysis,
  setScoringData,
}) => {
  const [showViewButton, _setShowViewButton] = useState<boolean>(() => !!dataAnalysis);

  const [dataAnalysisAI, setDataAnalysisAI] = useState<string>(() => dataAnalysis);

  const [files, setFiles] = useState<IFileResponse[]>(() => fileAnalysis);

  const [isSaved, _setIsSaved] = useState(true);

  const [isShowModal, setIsShowModal] = useState(false);

  const [initialFile, _setInitialFile] = useState<IFileResponse[]>(() => fileAnalysis);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { refetch } = useProjectStatisticRaw(idQuestionnaire);

  const { registerStep, clearStep } = useDirty();

  const params = useParams<{ id: string }>();

  const abortControllerRef = useRef<AbortController | null>(null);

  const titleConfirm = 'Confirm Changes';

  const descriptionConfirm = 'Are you sure you want to make this change? Changing the status will result in the deletion of all related information for this step';

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes in the form. Are you sure you want to leave?',
  });

  const { mutateAsync: refreshData } = useUpdateStatusStep();

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);

    const isChangedFiled = !compareObjectArray(initialFile, uploadedFiles);

    const isChangedModel = modelDefault !== modelSelected;
    _setIsSaved(!(isChangedFiled) || !isChangedModel);

    registerStep(stepId, () => (isChangedFiled || isChangedModel));
  }, []);

  const handleGetRawData = async () => {
    const { data } = await refetch();
    return data;
  };

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setDataAnalysisAI(dataAnalysis);
  }, [dataAnalysis]);

  const saveData = async (markdown: string, status: EStatusTask) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 5,
          type: EQuantitative.ANALYSIS,
          infos: [
            { form: markdown, status, isFinish: true },
          ],
          model: modelSelected,
        },

      ],
    };

    // await refreshData({
    //   id: stepId,
    //   status: EStatusTask.COMPLETED,
    //   select: 'all',
    //   isGenerate: true,
    //   stepIds: [],
    //   stepInfoIds: [],
    // });

    _setIsSaved(true);

    clearStep(stepId);

    await updateQuestionAnswer(
      payload,
      stepId,
    );
  };

  const handleSubmitData = async (markdown: string) => {
    saveData(markdown, EStatusTask.COMPLETED);
  };

  const getScoringContent = async (data: string) => {
    const payload = {
      project_id: params.id,
      evaluation_framework: evaluationFramework,
      content_to_score: data,
      llm: modelSelected,
    };
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.SCORING_CONTENT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const evaluationReport = res.data.result.evaluate_report;
      const evaluationScore = res.data.result.score;

      const data = { report: evaluationReport, score: evaluationScore };
      const payloadScoring = {
        formStepId: id,
        stepInfos: [
          {
            type: EQuantitative.SCORING,
            order: 6,
            infos: [{ value: data }],
            model: modelSelected,
          },
        ],
      };

      await updateQuestionAnswer(payloadScoring, stepId);

      setScoringData(data);
    } catch (error: any) {
      console.log(error);
    }
  };

  const isChanged = useMemo(() => {
    if (initialFile.length === 0 && files.length === 0) {
      return true;
    }
    return !compareObjectArray(initialFile, files);
  }, [initialFile, files]);

  const callDataFromAI = async () => {
    const survey = await handleGetRawData();

    const payload = {
      project_id: params.id,
      additional_info_url: [...getFile(files, true)],
      quantity_questionnaire_survey: { data: survey ?? {} },
      research_template_url: [...getFile(templates.length ? templates : [], true)],
      llm: modelSelected,
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.REPORT_QUANTITY }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const data = res.data.result;
      await getScoringContent(data);
      setDataAnalysisAI(data);
      saveData(data, EStatusTask.IN_PROGRESS);
      setIsAnalysis(true);
      setIsLoading(false);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useImperativeHandle(ref, () => {
    return {
      getAnalysis: async () => {
        await callDataFromAI();
      },
    };
  }, []);

  const handleViewData = () => {
    setIsAnalysis(true);
  };

  const saveDataInfos = async () => {
    clearStep(stepId);
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 4,
          type: EQuantitative.FILES,
          infos: [{
            files: files.map(file => ({
              ...file,
              file: file.key,
              name: file.originalname,
              type: file.mimeType,
              id: file._id,
            })),
          }],
          model: modelSelected,
        },
      ],
    };
    setIsLoading(true);
    await updateQuestionAnswer(
      payload,
      stepId,
    );

    callDataFromAI();
  };

  const handleGenerateData = async () => {
    const isChanged = !compareObjectArray(initialFile, files);

    const isChangedModel = modelDefault !== modelSelected;

    clearStep(stepId);
    _setIsSaved(false);
    if ((isChanged || isChangedModel) && dataAnalysisAI) {
      setIsShowModal(true);
      return;
    }

    if (!isChanged && dataAnalysisAI) {
      return;
    }

    setIsLoading(true);
    setIsAnalysis(true);
    await saveDataInfos();
  };

  const handleConfirmPopUp = async () => {
    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: [idAnalysis ?? '', idScoring ?? ''],
      select: 'all',
      isGenerate: true,
    });
    saveDataInfos ();
    setIsLoading(true);
    setDataAnalysisAI('');
    clearStep(stepId);
    setIsAnalysis(true);
    setIsShowModal(false);
    setScoringData(null);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  return (
    <React.Fragment>
      { isLoading
        ? (
            <div className="mt-4 w-full">
              Loading...
              <ProjectCardSkeleton />
              <MessageWarning />
            </div>
          )
        : (
            isAnalysis

              ? (
                  <div className="mt-4 relative">
                    <QuestionnaireAnalysis
                      stepId={stepId}
                      data={dataAnalysisAI}
                      isHiddenApproveButton={isFinish}
                      isFinish={isFinish}
                      isHiddenBackButton={true}
                      onBack={() => setIsAnalysis(false)}
                      onSubmit={handleSubmitData}
                    />
                  </div>
                )
              : (
                  <>
                    <div className=" mt-2 flex items-center justify-between p-4 border border-green-500 bg-green-50 rounded-md">
                      <div>
                        <h4 className="text-green-600 font-semibold text-lg">Ready to generate analysis?</h4>
                        <p className="text-green-500 text-sm">
                          The questionnaire will be closed once click Confirm.
                        </p>
                      </div>

                    </div>

                    <div className="mt-4">
                      <FileUpload onFilesChange={handleFilesChange} initialFile={initialFile} />
                    </div>

                    <div className={`sticky bottom-0 left-0 right-0  border-border p-4 z-20 flex justify-center gap-4 `}>

                      {showViewButton && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleViewData}
                        >
                          View Analysis
                        </Button>
                      )}

                      <Button
                        disabled={!isChanged}
                        type="button"
                        onClick={handleGenerateData}
                      >
                        Confirm
                      </Button>
                    </div>
                  </>
                )
          )}

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titleConfirm}
        description={descriptionConfirm}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText="Continue"
        cancelText="Cancel"
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </React.Fragment>
  );
};

export default AnalysisReportQuantitative;
