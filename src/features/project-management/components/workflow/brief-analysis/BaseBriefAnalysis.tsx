import { But<PERSON> } from '@/shared/components/ui/button';
import { ArrowDownTrayIcon, CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import Editor from '@/shared/components/ui/editor/editor';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import type { EditorContentChanged } from '@/shared/types/global';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { RefreshCcw } from 'lucide-react';
import SelectModelAI from '../common/SelectModelAI';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import MessageWarning from '../common/MessageWarning';

type BaseBriefAnalysisProps = {
  isLoading: boolean;
  markdown: string;
  form: string;
  isEditMode: boolean;
  onEditToggle: () => void;
  onConfirmChange: () => void;
  onDiscardChange: () => void;
  onEditorChange: (data: EditorContentChanged) => void;
  onApprove: () => void;
  onReGen?: () => void;
  onDownloadFile?: () => void;
  onChangeModel?: (data: string) => void;
  isShowEditButton?: boolean;
  isShowButtonReGen?: boolean;
  isShowButtonReGenDropdown?: boolean;
  modelAIDefault?: string;
};

const BaseBriefAnalysis: React.FC<BaseBriefAnalysisProps> = ({
  isLoading,
  markdown,
  isEditMode,
  isShowEditButton,
  isShowButtonReGen = false,
  isShowButtonReGenDropdown = false,
  modelAIDefault = EValueModelAI.GPT,
  onEditToggle,
  onConfirmChange,
  onDiscardChange,
  onEditorChange,
  onDownloadFile,
  onApprove,
  onReGen,
  onChangeModel,
}) => {
  const handleReGen = () => {
    if (onReGen) {
      onReGen();
    }
  };

  const handleDownloadFile = () => {
    if (onDownloadFile) {
      onDownloadFile();
    }
  };

  const handleChangeModelAI = (data: string) => {
    if (onChangeModel) {
      onChangeModel(data);
    }
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Analyzing</div>
          <ProjectCardSkeleton />

          <MessageWarning />
        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <SelectModelAI
            onChangeModel={handleChangeModelAI}
            defaultValue={modelAIDefault}
            isShowReGenButton={isShowButtonReGenDropdown}
            onReGen={handleReGen}
            disable={false}
            top="top-55"
            right="right-10"
          />

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6 z-1">
            {isEditMode
              ? (
                  <>
                    <Button type="button" variant="outline" onClick={onDiscardChange}>
                      Discard Change
                    </Button>
                    <Button type="button" onClick={onConfirmChange}>
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Confirm
                    </Button>
                  </>
                )
              : (
                  <>
                    {isShowButtonReGen && (
                      <Button onClick={handleReGen} type="button" variant="outline" className="text-cyan-500 bg-cyan-50">
                        <RefreshCcw className="h-5 w-5 " />
                      </Button>
                    )}
                    {!isShowEditButton && (
                      <Button type="button" variant="outline" onClick={onEditToggle}>
                        <FileEditIcon className="h-5 w-5 " />
                        Edit
                      </Button>
                    )}
                    <Button type="button" variant="outline" onClick={handleDownloadFile}>
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button>
                    <Button type="button" onClick={onApprove}>
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Approve
                    </Button>
                  </>
                )}
          </div>

          <div className="mt-6">
            {isEditMode
              ? (
                  <Editor onChange={e => onEditorChange(e)} value={markdown} />
                )
              : (
                  <MarkdownRenderer content={markdown} />
                )}
          </div>
        </div>
      );
};

export default BaseBriefAnalysis;
