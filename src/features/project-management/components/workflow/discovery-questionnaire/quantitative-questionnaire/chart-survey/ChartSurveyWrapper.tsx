'use client';

import ProjectCardSkeleton from '@/features/project-management/components/project-list/ProjectCardSkeleton';
import { useProjectSurvey } from '@/features/project-management/hooks';
import PieChartSurveyInfo from './PieChartSurveyInfo';
import { QuestionType } from '@/features/project-management/types/workflow';
import HorizontalBarChartInfo from './HorizontalBarChart';
import VerticalBarChartInfo from './VerticalBarChart';
import ListHorizontalBarChartInfo from './ListHorizontalBarChart';
import { dateToDDMMYYHHMM } from '@/shared/utils/date';
import { useEffect } from 'react';

type ChartSurveyWrapperType = {

  idQuestionnaire: string;
  setIsLoading: (status: boolean) => void;

};

const ChartSurveyWrapper: React.FC<ChartSurveyWrapperType> = ({
  idQuestionnaire,
  setIsLoading,
}) => {
  const { data: statisticVisual, isLoading: isLoadingData } = useProjectSurvey(idQuestionnaire);

  useEffect(() => {
    setIsLoading(isLoadingData);
  }, [isLoadingData, setIsLoading]);

  const startDate = statisticVisual && statisticVisual.questionnaireFormAnalyze.startDate ? dateToDDMMYYHHMM(statisticVisual.questionnaireFormAnalyze.startDate) : '?';
  const endDate = statisticVisual && statisticVisual.questionnaireFormAnalyze.endDate ? dateToDDMMYYHHMM(statisticVisual.questionnaireFormAnalyze.endDate) : '?';

  return (
    <div className="w-full relative">

      {
        !statisticVisual
          ? (
              <div className="mt-4">
                <div className="mb-2">Loading...</div>
                <ProjectCardSkeleton />
              </div>
            )
          : (
              <>
                {/* Overview */}
                <div className="bg-gray-50 w-full mt-4 p-4 rounded-xl">
                  <h4 className="text-gray-500 mb-1">
                    Optical Market Research Questionnaire
                  </h4>

                  <div className="flex items-center">
                    <div className="flex-2">
                      <p className="text-nowrap">Time :</p>
                      <ul className="list-disc pl-6">
                        <li>
                          {`${startDate} - ${endDate}`}
                        </li>
                      </ul>
                    </div>
                    <div className="flex flex-1 items-center gap-4">
                      <div className="flex-1">
                        <p className="text-nowrap">Number of responses :</p>
                        <ul className="list-disc pl-6">
                          <li>{statisticVisual.questionnaireFormAnalyze.numberOfResponses}</li>
                        </ul>
                      </div>

                      <div className="flex-1">
                        <p className="text-nowrap">Status:</p>
                        <ul className="list-disc pl-6">
                          <li className={` ${statisticVisual.questionnaireFormAnalyze.isActive ? 'text-green-500' : 'text-red-500'}`}>{statisticVisual.questionnaireFormAnalyze.isActive ? 'Active' : 'InActive'}</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                </div>
                {/* Sections */}
                { statisticVisual.sections.map((section, index) => (
                  <div key={index} className="p-4 rounded-xl bg-gray-50 w-full mt-4 ">
                    <div className=" p-1.5 px-3 border-l-4 border-black">
                      <h4 className="text-black">{section.title}</h4>
                      {/* <div
                        className=""
                      >
                        Please provide us your basic information for survey screening process.
                      </div> */}
                    </div>

                    {section.questions.map((question, idx) => (
                      <div key={idx} className="my-4">
                        {question.type === QuestionType.RADIO && (
                          <PieChartSurveyInfo title={question.title} data={question.statitics} />
                        )}

                        {question.type === QuestionType.CHECKBOX && (
                          <HorizontalBarChartInfo title={question.title} data={question.statitics} />
                        )}

                        {question.type === QuestionType.EVALUATION && (
                          <VerticalBarChartInfo title={question.title} legendLabel={question.options} data={question.statitics} />
                        )}

                        {question.type === QuestionType.CATEGORY && (
                          <ListHorizontalBarChartInfo title={question.title} subQuestions={question.subQuestions} />
                        )}
                      </div>
                    ))}
                  </div>
                ))}
              </>
            )
      }
    </div>

  );
};

export default ChartSurveyWrapper;
